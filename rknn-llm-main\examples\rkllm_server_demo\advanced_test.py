#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKLLM 高级功能测试脚本
测试 Function Calling、并发处理、压力测试等高级功能
"""

import requests
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics
import sys

class AdvancedRKLLMTester:
    def __init__(self, server_url='http://127.0.0.1:8080/rkllm_chat'):
        self.server_url = server_url
        self.session = requests.Session()
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': 'not_required'
        }
        
        # Function Calling 工具定义
        self.TOOLS = [
            {
                "type": "function",
                "function": {
                    "name": "get_current_temperature",
                    "description": "Get current temperature at a location.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": 'The location to get the temperature for, in the format "City, State, Country".',
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["celsius", "fahrenheit"],
                                "description": 'The unit to return the temperature in. Defaults to "celsius".',
                            },
                        },
                        "required": ["location"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "calculate_math",
                    "description": "Calculate mathematical expressions.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "expression": {
                                "type": "string",
                                "description": "Mathematical expression to calculate",
                            }
                        },
                        "required": ["expression"],
                    },
                },
            }
        ]
    
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def test_function_calling(self):
        """测试 Function Calling 功能"""
        self.log("=== 开始 Function Calling 测试 ===")
        
        test_cases = [
            "北京现在的温度是多少？",
            "上海的温度是多少摄氏度？",
            "计算 15 + 25 * 3 的结果",
            "帮我算一下 (100 - 25) / 5 等于多少"
        ]
        
        for i, question in enumerate(test_cases, 1):
            self.log(f"Function Calling 测试 {i}: {question}")
            
            try:
                # 第一次请求 - 获取函数调用
                messages = [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": question}
                ]
                
                data = {
                    "model": "test",
                    "messages": messages,
                    "stream": False,
                    "enable_thinking": False,
                    "tools": self.TOOLS
                }
                
                response = self.session.post(self.server_url, json=data, headers=self.headers, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    server_answer = result["choices"][0]["message"]["content"]
                    self.log(f"✓ 模型响应: {server_answer[:100]}...")
                    
                    # 检查是否包含工具调用
                    if "<tool_call>" in server_answer:
                        self.log("✓ 检测到工具调用")
                        
                        # 模拟工具执行结果
                        if "temperature" in question.lower() or "温度" in question:
                            tool_result = {"temperature": 25.5, "location": "北京", "unit": "celsius"}
                        else:
                            tool_result = {"result": 42}
                        
                        # 添加工具结果并再次请求
                        messages.append({"role": "assistant", "content": server_answer})
                        messages.append({"role": "tool", "name": "tool_response", "content": json.dumps(tool_result)})
                        
                        data["messages"] = messages
                        final_response = self.session.post(self.server_url, json=data, headers=self.headers, timeout=30)
                        
                        if final_response.status_code == 200:
                            final_result = final_response.json()
                            final_answer = final_result["choices"][0]["message"]["content"]
                            self.log(f"✓ 最终回答: {final_answer[:100]}...")
                        else:
                            self.log("✗ 工具结果处理失败", "ERROR")
                    else:
                        self.log("⚠ 未检测到工具调用", "WARNING")
                else:
                    self.log(f"✗ 请求失败: {response.status_code}", "ERROR")
                    
            except Exception as e:
                self.log(f"✗ Function Calling 测试失败: {str(e)}", "ERROR")
            
            time.sleep(2)
    
    def test_streaming_output(self):
        """测试流式输出功能"""
        self.log("=== 开始流式输出测试 ===")
        
        question = "请详细介绍一下人工智能的发展历史，包括重要的里程碑事件"
        
        try:
            data = {
                "model": "test",
                "messages": [{"role": "user", "content": question}],
                "stream": True
            }
            
            start_time = time.time()
            response = self.session.post(self.server_url, json=data, headers=self.headers, stream=True, timeout=60)
            
            if response.status_code == 200:
                self.log("✓ 开始接收流式数据")
                
                token_count = 0
                response_text = ""
                
                for line in response.iter_lines():
                    if line:
                        try:
                            line_data = json.loads(line.decode('utf-8'))
                            if line_data["choices"][0]["finish_reason"] != "stop":
                                content = line_data["choices"][0]["delta"]["content"]
                                response_text += content
                                token_count += 1
                                
                                # 显示实时进度
                                if token_count % 10 == 0:
                                    elapsed = time.time() - start_time
                                    rate = token_count / elapsed if elapsed > 0 else 0
                                    print(f"\r进度: {token_count} tokens, 速率: {rate:.1f} tokens/s", end="")
                        except:
                            continue
                
                total_time = time.time() - start_time
                final_rate = token_count / total_time if total_time > 0 else 0
                
                print()  # 换行
                self.log(f"✓ 流式输出完成")
                self.log(f"✓ 总tokens: {token_count}, 总时间: {total_time:.2f}s, 平均速率: {final_rate:.2f} tokens/s")
                self.log(f"✓ 响应内容长度: {len(response_text)} 字符")
                
            else:
                self.log(f"✗ 流式请求失败: {response.status_code}", "ERROR")
                
        except Exception as e:
            self.log(f"✗ 流式输出测试失败: {str(e)}", "ERROR")
    
    def test_concurrent_requests(self, num_threads=3):
        """测试并发请求处理"""
        self.log(f"=== 开始并发测试 ({num_threads} 个并发请求) ===")
        
        questions = [
            "请介绍一下机器学习",
            "什么是深度学习？",
            "人工智能的应用领域有哪些？",
            "请解释一下神经网络",
            "什么是自然语言处理？"
        ]
        
        def send_request(question, thread_id):
            """单个请求函数"""
            start_time = time.time()
            try:
                data = {
                    "model": "test",
                    "messages": [{"role": "user", "content": question}],
                    "stream": False
                }
                
                response = requests.post(self.server_url, json=data, headers=self.headers, timeout=30)
                end_time = time.time()
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    return {
                        'thread_id': thread_id,
                        'question': question,
                        'success': True,
                        'response_time': end_time - start_time,
                        'response_length': len(content)
                    }
                else:
                    return {
                        'thread_id': thread_id,
                        'question': question,
                        'success': False,
                        'error': f"HTTP {response.status_code}"
                    }
                    
            except Exception as e:
                return {
                    'thread_id': thread_id,
                    'question': question,
                    'success': False,
                    'error': str(e)
                }
        
        # 执行并发请求
        start_time = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = []
            for i in range(num_threads):
                question = questions[i % len(questions)]
                future = executor.submit(send_request, question, i+1)
                futures.append(future)
            
            for future in as_completed(futures):
                result = future.result()
                results.append(result)
                
                if result['success']:
                    self.log(f"✓ 线程 {result['thread_id']} 完成: {result['response_time']:.2f}s")
                else:
                    self.log(f"✗ 线程 {result['thread_id']} 失败: {result['error']}", "ERROR")
        
        total_time = time.time() - start_time
        success_count = sum(1 for r in results if r['success'])
        
        self.log(f"并发测试结果: {success_count}/{num_threads} 成功")
        self.log(f"总耗时: {total_time:.2f}s")
        
        if success_count > 0:
            avg_response_time = statistics.mean([r['response_time'] for r in results if r['success']])
            self.log(f"平均响应时间: {avg_response_time:.2f}s")
    
    def test_model_quality(self):
        """测试模型回答质量"""
        self.log("=== 开始模型质量测试 ===")
        
        quality_tests = [
            {
                "question": "请解释什么是机器学习",
                "keywords": ["算法", "数据", "模型", "训练", "预测"],
                "min_length": 50
            },
            {
                "question": "1+1等于几？",
                "expected_answer": "2",
                "max_length": 100
            },
            {
                "question": "请列举三种编程语言",
                "keywords": ["Python", "Java", "C++", "JavaScript", "Go", "Rust"],
                "min_keywords": 3
            }
        ]
        
        for i, test in enumerate(quality_tests, 1):
            self.log(f"质量测试 {i}: {test['question']}")
            
            try:
                data = {
                    "model": "test",
                    "messages": [{"role": "user", "content": test['question']}],
                    "stream": False
                }
                
                response = self.session.post(self.server_url, json=data, headers=self.headers, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result["choices"][0]["message"]["content"]
                    
                    self.log(f"回答: {answer[:100]}...")
                    
                    # 质量检查
                    quality_score = 0
                    checks = []
                    
                    # 长度检查
                    if 'min_length' in test:
                        if len(answer) >= test['min_length']:
                            quality_score += 1
                            checks.append("✓ 长度合适")
                        else:
                            checks.append(f"✗ 长度不足 ({len(answer)}/{test['min_length']})")
                    
                    if 'max_length' in test:
                        if len(answer) <= test['max_length']:
                            quality_score += 1
                            checks.append("✓ 长度合适")
                        else:
                            checks.append(f"✗ 长度过长 ({len(answer)}/{test['max_length']})")
                    
                    # 关键词检查
                    if 'keywords' in test:
                        found_keywords = [kw for kw in test['keywords'] if kw.lower() in answer.lower()]
                        min_kw = test.get('min_keywords', 1)
                        
                        if len(found_keywords) >= min_kw:
                            quality_score += 1
                            checks.append(f"✓ 包含关键词: {found_keywords}")
                        else:
                            checks.append(f"✗ 关键词不足: {found_keywords}")
                    
                    # 精确答案检查
                    if 'expected_answer' in test:
                        if test['expected_answer'] in answer:
                            quality_score += 1
                            checks.append("✓ 答案正确")
                        else:
                            checks.append("✗ 答案不正确")
                    
                    for check in checks:
                        self.log(f"  {check}")
                    
                    self.log(f"质量评分: {quality_score}/{len(checks)}")
                    
                else:
                    self.log(f"✗ 请求失败: {response.status_code}", "ERROR")
                    
            except Exception as e:
                self.log(f"✗ 质量测试失败: {str(e)}", "ERROR")
            
            time.sleep(1)

def main():
    """主测试流程"""
    print("RKLLM 高级功能测试工具")
    print("=" * 50)
    
    tester = AdvancedRKLLMTester()
    
    try:
        # 选择测试项目
        print("请选择要执行的测试:")
        print("1. Function Calling 测试")
        print("2. 流式输出测试")
        print("3. 并发请求测试")
        print("4. 模型质量测试")
        print("5. 全部测试")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            tester.test_function_calling()
        elif choice == "2":
            tester.test_streaming_output()
        elif choice == "3":
            tester.test_concurrent_requests()
        elif choice == "4":
            tester.test_model_quality()
        elif choice == "5":
            tester.test_function_calling()
            tester.test_streaming_output()
            tester.test_concurrent_requests()
            tester.test_model_quality()
        else:
            print("无效选择")
            return
        
        print("\n测试完成!")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
