#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKLLM 模型综合测试脚本
用于全面测试 Qwen3-1.7B.rkllm 模型的各项功能和性能
"""

import requests
import json
import time
import threading
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys

class RKLLMTester:
    def __init__(self, server_url='http://127.0.0.1:8080/rkllm_chat'):
        self.server_url = server_url
        self.session = requests.Session()
        self.session.keep_alive = False
        adapter = requests.adapters.HTTPAdapter(max_retries=3)
        self.session.mount('https://', adapter)
        self.session.mount('http://', adapter)
        
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': 'not_required'
        }
        
        # 测试结果存储
        self.test_results = {
            'basic_tests': [],
            'performance_tests': [],
            'function_tests': [],
            'stress_tests': [],
            'quality_tests': []
        }
    
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def test_connection(self):
        """测试服务器连接"""
        self.log("=== 开始连接测试 ===")
        try:
            # 发送简单请求测试连接
            data = {
                "model": "test",
                "messages": [{"role": "user", "content": "hello"}],
                "stream": False
            }
            
            response = self.session.post(self.server_url, json=data, headers=self.headers, timeout=30)
            if response.status_code == 200:
                self.log("✓ 服务器连接成功", "SUCCESS")
                return True
            else:
                self.log(f"✗ 服务器响应错误: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"✗ 连接失败: {str(e)}", "ERROR")
            return False
    
    def basic_functionality_test(self):
        """基础功能测试"""
        self.log("=== 开始基础功能测试 ===")
        
        test_cases = [
            "你好",
            "请介绍一下你自己",
            "1+1等于几？",
            "请写一首关于春天的诗",
            "解释一下什么是人工智能",
        ]
        
        for i, question in enumerate(test_cases, 1):
            self.log(f"测试用例 {i}: {question}")
            
            start_time = time.time()
            success, response_data = self._send_request(question, stream=False)
            end_time = time.time()
            
            if success:
                response_time = end_time - start_time
                content = response_data["choices"][0]["message"]["content"]
                
                result = {
                    'question': question,
                    'response': content,
                    'response_time': response_time,
                    'success': True
                }
                
                self.log(f"✓ 响应时间: {response_time:.2f}s")
                self.log(f"✓ 回答: {content[:100]}...")
                
            else:
                result = {
                    'question': question,
                    'response': None,
                    'response_time': None,
                    'success': False
                }
                self.log(f"✗ 请求失败")
            
            self.test_results['basic_tests'].append(result)
            time.sleep(1)  # 避免请求过快
    
    def performance_test(self):
        """性能测试"""
        self.log("=== 开始性能测试 ===")
        
        # 测试不同长度的输入
        test_cases = [
            ("短文本", "你好"),
            ("中等文本", "请详细解释一下机器学习的基本概念，包括监督学习、无监督学习和强化学习的区别"),
            ("长文本", "请写一篇关于人工智能发展历史的文章，包括从图灵测试到现代深度学习的发展过程，以及未来可能的发展方向" * 3)
        ]
        
        for test_name, question in test_cases:
            self.log(f"性能测试: {test_name}")
            
            # 测试流式输出
            start_time = time.time()
            success, tokens_per_second = self._test_streaming_performance(question)
            end_time = time.time()
            
            if success:
                result = {
                    'test_type': test_name,
                    'question_length': len(question),
                    'total_time': end_time - start_time,
                    'tokens_per_second': tokens_per_second,
                    'success': True
                }
                self.log(f"✓ {test_name} - 总时间: {result['total_time']:.2f}s, Token速率: {tokens_per_second:.2f} tokens/s")
            else:
                result = {
                    'test_type': test_name,
                    'success': False
                }
                self.log(f"✗ {test_name} - 测试失败")
            
            self.test_results['performance_tests'].append(result)
            time.sleep(2)
    
    def _test_streaming_performance(self, question):
        """测试流式输出性能"""
        try:
            data = {
                "model": "test",
                "messages": [{"role": "user", "content": question}],
                "stream": True
            }
            
            response = self.session.post(self.server_url, json=data, headers=self.headers, stream=True, timeout=60)
            
            if response.status_code != 200:
                return False, 0
            
            token_count = 0
            start_time = time.time()
            last_time = start_time
            
            for line in response.iter_lines():
                if line:
                    try:
                        line_data = json.loads(line.decode('utf-8'))
                        if line_data["choices"][0]["finish_reason"] != "stop":
                            token_count += 1
                            last_time = time.time()
                    except:
                        continue
            
            total_time = last_time - start_time
            tokens_per_second = token_count / total_time if total_time > 0 else 0
            
            return True, tokens_per_second
            
        except Exception as e:
            self.log(f"流式测试错误: {str(e)}", "ERROR")
            return False, 0
    
    def _send_request(self, question, stream=False, enable_thinking=False, tools=None):
        """发送请求的通用方法"""
        try:
            data = {
                "model": "test",
                "messages": [{"role": "user", "content": question}],
                "stream": stream,
                "enable_thinking": enable_thinking
            }
            
            if tools:
                data["tools"] = tools
            
            response = self.session.post(self.server_url, json=data, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                if stream:
                    return True, response
                else:
                    return True, response.json()
            else:
                return False, None
                
        except Exception as e:
            self.log(f"请求错误: {str(e)}", "ERROR")
            return False, None
    
    def generate_report(self):
        """生成测试报告"""
        self.log("=== 生成测试报告 ===")
        
        report = []
        report.append("=" * 60)
        report.append("RKLLM 模型测试报告")
        report.append("=" * 60)
        report.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"服务器地址: {self.server_url}")
        report.append("")
        
        # 基础功能测试报告
        basic_tests = self.test_results['basic_tests']
        if basic_tests:
            success_count = sum(1 for test in basic_tests if test['success'])
            report.append(f"基础功能测试: {success_count}/{len(basic_tests)} 通过")
            
            if success_count > 0:
                avg_response_time = statistics.mean([test['response_time'] for test in basic_tests if test['success']])
                report.append(f"平均响应时间: {avg_response_time:.2f}秒")
            report.append("")
        
        # 性能测试报告
        perf_tests = self.test_results['performance_tests']
        if perf_tests:
            success_tests = [test for test in perf_tests if test['success']]
            if success_tests:
                avg_tokens_per_sec = statistics.mean([test['tokens_per_second'] for test in success_tests])
                report.append(f"性能测试: {len(success_tests)}/{len(perf_tests)} 通过")
                report.append(f"平均Token生成速率: {avg_tokens_per_sec:.2f} tokens/s")
                report.append("")
        
        report.append("=" * 60)
        
        # 输出报告
        for line in report:
            print(line)
        
        # 保存报告到文件
        with open('rkllm_test_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        self.log("测试报告已保存到 rkllm_test_report.txt")

def main():
    """主测试流程"""
    print("RKLLM 模型综合测试工具")
    print("=" * 50)
    
    # 初始化测试器
    tester = RKLLMTester()
    
    # 测试连接
    if not tester.test_connection():
        print("无法连接到服务器，请确保 flask_server.py 已启动")
        return
    
    try:
        # 执行测试
        tester.basic_functionality_test()
        tester.performance_test()
        
        # 生成报告
        tester.generate_report()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
