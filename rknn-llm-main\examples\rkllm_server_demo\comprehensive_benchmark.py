#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKLLM 模型全面基准测试框架
包含性能、功能、质量、鲁棒性、一致性等多维度评估
"""

import requests
import json
import time
import threading
import statistics
import re
import hashlib
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import List, Dict, Any
import sys

@dataclass
class TestResult:
    """测试结果数据结构"""
    test_name: str
    category: str
    score: float
    max_score: float
    details: Dict[str, Any]
    timestamp: float

class ComprehensiveBenchmark:
    def __init__(self, server_url='http://127.0.0.1:8080/rkllm_chat'):
        self.server_url = server_url
        self.session = requests.Session()
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': 'not_required'
        }
        self.results: List[TestResult] = []
        
        # 测试数据集
        self.test_datasets = self._load_test_datasets()
    
    def _load_test_datasets(self):
        """加载测试数据集"""
        return {
            'qa_accuracy': [
                {"question": "中国的首都是哪里？", "expected": "北京", "type": "factual"},
                {"question": "1+1等于几？", "expected": "2", "type": "math"},
                {"question": "水的化学分子式是什么？", "expected": "H2O", "type": "science"},
                {"question": "《红楼梦》的作者是谁？", "expected": "曹雪芹", "type": "literature"},
                {"question": "Python是什么？", "expected": "编程语言", "type": "technology"}
            ],
            'reasoning': [
                {"question": "如果所有的鸟都会飞，企鹅是鸟，那么企鹅会飞吗？请解释原因。", "type": "logic"},
                {"question": "一个班级有30个学生，其中60%是女生，那么男生有多少人？", "type": "math_reasoning"},
                {"question": "为什么天空是蓝色的？", "type": "scientific_reasoning"}
            ],
            'creativity': [
                {"prompt": "写一首关于春天的诗", "type": "poetry"},
                {"prompt": "编一个关于机器人的故事", "type": "storytelling"},
                {"prompt": "设计一个新的手机App的创意", "type": "innovation"}
            ],
            'code_generation': [
                {"prompt": "写一个Python函数计算斐波那契数列", "type": "algorithm"},
                {"prompt": "用JavaScript实现一个简单的计算器", "type": "web_dev"},
                {"prompt": "写一个SQL查询语句查找最高薪资的员工", "type": "database"}
            ],
            'multilingual': [
                {"question": "Hello, how are you?", "language": "English"},
                {"question": "Bonjour, comment allez-vous?", "language": "French"},
                {"question": "こんにちは、元気ですか？", "language": "Japanese"}
            ],
            'edge_cases': [
                {"input": "", "type": "empty"},
                {"input": "a" * 1000, "type": "very_long"},
                {"input": "!@#$%^&*()", "type": "special_chars"},
                {"input": "请重复说100遍'你好'", "type": "repetitive_request"}
            ]
        }
    
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def _send_request(self, content, stream=False, timeout=30):
        """发送请求的通用方法"""
        try:
            data = {
                "model": "benchmark",
                "messages": [{"role": "user", "content": content}],
                "stream": stream
            }
            
            response = self.session.post(
                self.server_url, 
                json=data, 
                headers=self.headers, 
                timeout=timeout,
                stream=stream
            )
            
            if response.status_code == 200:
                if stream:
                    return True, response
                else:
                    return True, response.json()
            else:
                return False, f"HTTP {response.status_code}"
                
        except Exception as e:
            return False, str(e)
    
    def test_performance_metrics(self):
        """性能指标测试"""
        self.log("=== 开始性能指标测试 ===")
        
        test_cases = [
            ("短文本", "你好"),
            ("中等文本", "请详细解释一下机器学习的基本概念和应用领域"),
            ("长文本", "请写一篇关于人工智能发展历史的详细文章，包括重要里程碑、技术突破和未来展望" * 3)
        ]
        
        performance_results = {}
        
        for test_name, content in test_cases:
            self.log(f"测试 {test_name} 性能")
            
            # 测试首Token时间和总响应时间
            ttft_times = []
            total_times = []
            token_rates = []
            
            for i in range(3):  # 重复3次取平均值
                start_time = time.time()
                success, response = self._send_request(content, stream=True)
                
                if success:
                    first_token_time = None
                    token_count = 0
                    
                    for line in response.iter_lines():
                        if line:
                            try:
                                line_data = json.loads(line.decode('utf-8'))
                                if line_data["choices"][0]["finish_reason"] != "stop":
                                    if first_token_time is None:
                                        first_token_time = time.time() - start_time
                                    token_count += 1
                            except:
                                continue
                    
                    total_time = time.time() - start_time
                    
                    if first_token_time:
                        ttft_times.append(first_token_time)
                    total_times.append(total_time)
                    
                    if total_time > 0:
                        token_rates.append(token_count / total_time)
                
                time.sleep(1)  # 避免请求过快
            
            # 计算平均值
            if ttft_times and total_times and token_rates:
                avg_ttft = statistics.mean(ttft_times)
                avg_total = statistics.mean(total_times)
                avg_rate = statistics.mean(token_rates)
                
                # 评分标准
                ttft_score = self._score_ttft(avg_ttft)
                rate_score = self._score_token_rate(avg_rate)
                
                performance_results[test_name] = {
                    'avg_ttft': avg_ttft,
                    'avg_total_time': avg_total,
                    'avg_token_rate': avg_rate,
                    'ttft_score': ttft_score,
                    'rate_score': rate_score
                }
                
                self.log(f"✓ {test_name} - TTFT: {avg_ttft:.2f}s, 总时间: {avg_total:.2f}s, Token速率: {avg_rate:.2f} tokens/s")
        
        # 记录结果
        overall_score = statistics.mean([
            result['ttft_score'] + result['rate_score'] 
            for result in performance_results.values()
        ]) if performance_results else 0
        
        self.results.append(TestResult(
            test_name="性能指标测试",
            category="Performance",
            score=overall_score,
            max_score=200,  # TTFT(100) + TokenRate(100)
            details=performance_results,
            timestamp=time.time()
        ))
    
    def test_qa_accuracy(self):
        """问答准确性测试"""
        self.log("=== 开始问答准确性测试 ===")
        
        correct_answers = 0
        total_questions = len(self.test_datasets['qa_accuracy'])
        detailed_results = []
        
        for qa in self.test_datasets['qa_accuracy']:
            self.log(f"测试问题: {qa['question']}")
            
            success, response = self._send_request(qa['question'])
            
            if success:
                answer = response["choices"][0]["message"]["content"]
                is_correct = self._check_answer_correctness(answer, qa['expected'])
                
                if is_correct:
                    correct_answers += 1
                    self.log(f"✓ 回答正确")
                else:
                    self.log(f"✗ 回答错误 - 期望: {qa['expected']}, 实际: {answer[:50]}...")
                
                detailed_results.append({
                    'question': qa['question'],
                    'expected': qa['expected'],
                    'actual': answer,
                    'correct': is_correct,
                    'type': qa['type']
                })
            else:
                self.log(f"✗ 请求失败: {response}")
                detailed_results.append({
                    'question': qa['question'],
                    'expected': qa['expected'],
                    'actual': None,
                    'correct': False,
                    'type': qa['type'],
                    'error': response
                })
            
            time.sleep(1)
        
        accuracy = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
        
        self.results.append(TestResult(
            test_name="问答准确性测试",
            category="Accuracy",
            score=accuracy,
            max_score=100,
            details={
                'correct_answers': correct_answers,
                'total_questions': total_questions,
                'accuracy_rate': accuracy,
                'detailed_results': detailed_results
            },
            timestamp=time.time()
        ))
        
        self.log(f"问答准确性: {correct_answers}/{total_questions} ({accuracy:.1f}%)")
    
    def test_consistency(self):
        """一致性测试"""
        self.log("=== 开始一致性测试 ===")
        
        test_questions = [
            "请介绍一下人工智能",
            "1+1等于几？",
            "中国的首都是哪里？"
        ]
        
        consistency_scores = []
        detailed_results = []
        
        for question in test_questions:
            self.log(f"一致性测试: {question}")
            
            responses = []
            for i in range(3):  # 重复3次
                success, response = self._send_request(question)
                if success:
                    content = response["choices"][0]["message"]["content"]
                    responses.append(content)
                time.sleep(1)
            
            if len(responses) >= 2:
                # 计算响应相似度
                similarity_scores = []
                for i in range(len(responses)):
                    for j in range(i+1, len(responses)):
                        similarity = self._calculate_similarity(responses[i], responses[j])
                        similarity_scores.append(similarity)
                
                avg_similarity = statistics.mean(similarity_scores) if similarity_scores else 0
                consistency_scores.append(avg_similarity)
                
                detailed_results.append({
                    'question': question,
                    'responses': responses,
                    'similarity_scores': similarity_scores,
                    'avg_similarity': avg_similarity
                })
                
                self.log(f"✓ 一致性得分: {avg_similarity:.2f}")
        
        overall_consistency = statistics.mean(consistency_scores) if consistency_scores else 0
        
        self.results.append(TestResult(
            test_name="一致性测试",
            category="Consistency",
            score=overall_consistency * 100,
            max_score=100,
            details={
                'overall_consistency': overall_consistency,
                'detailed_results': detailed_results
            },
            timestamp=time.time()
        ))
    
    def _score_ttft(self, ttft):
        """TTFT评分 (0-100)"""
        if ttft < 1.0:
            return 100
        elif ttft < 2.0:
            return 80
        elif ttft < 3.0:
            return 60
        else:
            return max(0, 60 - (ttft - 3.0) * 20)
    
    def _score_token_rate(self, rate):
        """Token速率评分 (0-100)"""
        if rate > 20:
            return 100
        elif rate > 15:
            return 80
        elif rate > 10:
            return 60
        else:
            return max(0, rate * 6)
    
    def _check_answer_correctness(self, answer, expected):
        """检查答案正确性"""
        answer_lower = answer.lower()
        expected_lower = expected.lower()
        return expected_lower in answer_lower
    
    def _calculate_similarity(self, text1, text2):
        """计算文本相似度 (简单的基于字符的相似度)"""
        # 使用Jaccard相似度
        set1 = set(text1)
        set2 = set(text2)
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0
    
    def test_robustness(self):
        """鲁棒性测试"""
        self.log("=== 开始鲁棒性测试 ===")

        robustness_scores = []
        detailed_results = []

        for edge_case in self.test_datasets['edge_cases']:
            self.log(f"鲁棒性测试: {edge_case['type']}")

            success, response = self._send_request(edge_case['input'], timeout=15)

            if success:
                content = response["choices"][0]["message"]["content"]
                # 评估响应质量
                quality_score = self._evaluate_edge_case_response(content, edge_case['type'])
                robustness_scores.append(quality_score)

                detailed_results.append({
                    'input': edge_case['input'][:100] + "..." if len(edge_case['input']) > 100 else edge_case['input'],
                    'type': edge_case['type'],
                    'response': content[:200] + "..." if len(content) > 200 else content,
                    'quality_score': quality_score,
                    'success': True
                })

                self.log(f"✓ 处理成功，质量得分: {quality_score}")
            else:
                robustness_scores.append(0)
                detailed_results.append({
                    'input': edge_case['input'][:100],
                    'type': edge_case['type'],
                    'response': None,
                    'quality_score': 0,
                    'success': False,
                    'error': response
                })
                self.log(f"✗ 处理失败: {response}")

            time.sleep(1)

        avg_robustness = statistics.mean(robustness_scores) if robustness_scores else 0

        self.results.append(TestResult(
            test_name="鲁棒性测试",
            category="Robustness",
            score=avg_robustness * 100,
            max_score=100,
            details={
                'average_score': avg_robustness,
                'detailed_results': detailed_results
            },
            timestamp=time.time()
        ))

    def test_reasoning_ability(self):
        """推理能力测试"""
        self.log("=== 开始推理能力测试 ===")

        reasoning_scores = []
        detailed_results = []

        for reasoning_case in self.test_datasets['reasoning']:
            self.log(f"推理测试: {reasoning_case['type']}")

            success, response = self._send_request(reasoning_case['question'])

            if success:
                content = response["choices"][0]["message"]["content"]
                reasoning_score = self._evaluate_reasoning_quality(content, reasoning_case['type'])
                reasoning_scores.append(reasoning_score)

                detailed_results.append({
                    'question': reasoning_case['question'],
                    'type': reasoning_case['type'],
                    'response': content,
                    'reasoning_score': reasoning_score
                })

                self.log(f"✓ 推理得分: {reasoning_score}")
            else:
                reasoning_scores.append(0)
                detailed_results.append({
                    'question': reasoning_case['question'],
                    'type': reasoning_case['type'],
                    'response': None,
                    'reasoning_score': 0,
                    'error': response
                })
                self.log(f"✗ 推理测试失败: {response}")

            time.sleep(1)

        avg_reasoning = statistics.mean(reasoning_scores) if reasoning_scores else 0

        self.results.append(TestResult(
            test_name="推理能力测试",
            category="Reasoning",
            score=avg_reasoning * 100,
            max_score=100,
            details={
                'average_score': avg_reasoning,
                'detailed_results': detailed_results
            },
            timestamp=time.time()
        ))

    def test_creativity(self):
        """创造性测试"""
        self.log("=== 开始创造性测试 ===")

        creativity_scores = []
        detailed_results = []

        for creative_case in self.test_datasets['creativity']:
            self.log(f"创造性测试: {creative_case['type']}")

            success, response = self._send_request(creative_case['prompt'])

            if success:
                content = response["choices"][0]["message"]["content"]
                creativity_score = self._evaluate_creativity(content, creative_case['type'])
                creativity_scores.append(creativity_score)

                detailed_results.append({
                    'prompt': creative_case['prompt'],
                    'type': creative_case['type'],
                    'response': content,
                    'creativity_score': creativity_score
                })

                self.log(f"✓ 创造性得分: {creativity_score}")
            else:
                creativity_scores.append(0)
                detailed_results.append({
                    'prompt': creative_case['prompt'],
                    'type': creative_case['type'],
                    'response': None,
                    'creativity_score': 0,
                    'error': response
                })
                self.log(f"✗ 创造性测试失败: {response}")

            time.sleep(1)

        avg_creativity = statistics.mean(creativity_scores) if creativity_scores else 0

        self.results.append(TestResult(
            test_name="创造性测试",
            category="Creativity",
            score=avg_creativity * 100,
            max_score=100,
            details={
                'average_score': avg_creativity,
                'detailed_results': detailed_results
            },
            timestamp=time.time()
        ))

    def _evaluate_edge_case_response(self, response, case_type):
        """评估边界情况响应质量"""
        if not response or len(response.strip()) == 0:
            return 0.0

        if case_type == "empty":
            # 空输入应该有合理的提示
            if "请" in response or "输入" in response or "问题" in response:
                return 0.8
            return 0.3
        elif case_type == "very_long":
            # 长输入应该能正常处理
            if len(response) > 10 and "错误" not in response:
                return 0.9
            return 0.2
        elif case_type == "special_chars":
            # 特殊字符应该能正常处理
            if len(response) > 5:
                return 0.7
            return 0.1
        elif case_type == "repetitive_request":
            # 重复请求应该被合理拒绝或限制
            if "不能" in response or "无法" in response or len(response) < 500:
                return 0.8
            return 0.3

        return 0.5

    def _evaluate_reasoning_quality(self, response, reasoning_type):
        """评估推理质量"""
        if not response:
            return 0.0

        response_lower = response.lower()

        if reasoning_type == "logic":
            # 逻辑推理应该包含推理过程
            logic_keywords = ["因为", "所以", "但是", "然而", "实际上", "推理", "逻辑"]
            found_keywords = sum(1 for kw in logic_keywords if kw in response)
            return min(found_keywords / 3, 1.0)

        elif reasoning_type == "math_reasoning":
            # 数学推理应该包含计算过程
            if "30" in response and ("60%" in response or "0.6" in response):
                if "12" in response or "十二" in response:
                    return 1.0
                return 0.7
            return 0.2

        elif reasoning_type == "scientific_reasoning":
            # 科学推理应该包含科学解释
            science_keywords = ["光", "散射", "波长", "大气", "分子"]
            found_keywords = sum(1 for kw in science_keywords if kw in response)
            return min(found_keywords / 3, 1.0)

        return 0.5

    def _evaluate_creativity(self, response, creativity_type):
        """评估创造性"""
        if not response or len(response) < 20:
            return 0.0

        if creativity_type == "poetry":
            # 诗歌应该有韵律和意境
            lines = response.split('\n')
            if len(lines) >= 4 and any(len(line.strip()) > 0 for line in lines):
                return 0.8
            return 0.4

        elif creativity_type == "storytelling":
            # 故事应该有情节
            story_elements = ["机器人", "故事", "开始", "结束", "情节"]
            found_elements = sum(1 for elem in story_elements if elem in response)
            return min(found_elements / 3, 1.0)

        elif creativity_type == "innovation":
            # 创新应该有新颖性
            if len(response) > 100 and ("功能" in response or "特色" in response):
                return 0.7
            return 0.3

        return 0.5

    def generate_comprehensive_report(self):
        """生成综合测试报告"""
        self.log("=== 生成综合测试报告 ===")

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("RKLLM 模型综合基准测试报告")
        report_lines.append("=" * 80)
        report_lines.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"服务器地址: {self.server_url}")
        report_lines.append("")

        # 按类别汇总结果
        categories = {}
        for result in self.results:
            if result.category not in categories:
                categories[result.category] = []
            categories[result.category].append(result)

        total_score = 0
        total_max_score = 0

        for category, results in categories.items():
            report_lines.append(f"【{category} 类别测试】")
            report_lines.append("-" * 40)

            category_score = 0
            category_max_score = 0

            for result in results:
                score_percentage = (result.score / result.max_score) * 100 if result.max_score > 0 else 0
                report_lines.append(f"{result.test_name}: {result.score:.1f}/{result.max_score} ({score_percentage:.1f}%)")

                category_score += result.score
                category_max_score += result.max_score

            if category_max_score > 0:
                category_percentage = (category_score / category_max_score) * 100
                report_lines.append(f"类别总分: {category_score:.1f}/{category_max_score} ({category_percentage:.1f}%)")

            total_score += category_score
            total_max_score += category_max_score
            report_lines.append("")

        # 总体评分
        if total_max_score > 0:
            overall_percentage = (total_score / total_max_score) * 100
            report_lines.append(f"【总体评分】")
            report_lines.append(f"综合得分: {total_score:.1f}/{total_max_score} ({overall_percentage:.1f}%)")

            # 评级
            if overall_percentage >= 90:
                grade = "优秀 (A)"
            elif overall_percentage >= 80:
                grade = "良好 (B)"
            elif overall_percentage >= 70:
                grade = "中等 (C)"
            elif overall_percentage >= 60:
                grade = "及格 (D)"
            else:
                grade = "需改进 (F)"

            report_lines.append(f"评级: {grade}")

        # 添加改进建议
        report_lines.append("")
        report_lines.append("【改进建议】")
        report_lines.append("-" * 40)

        for category, results in categories.items():
            for result in results:
                score_percentage = (result.score / result.max_score) * 100 if result.max_score > 0 else 0
                if score_percentage < 70:
                    report_lines.append(f"• {result.test_name}: 得分较低({score_percentage:.1f}%)，建议重点优化")

        report_lines.append("=" * 80)

        # 输出报告
        for line in report_lines:
            print(line)

        # 保存报告
        with open('comprehensive_benchmark_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        self.log("详细报告已保存到 comprehensive_benchmark_report.txt")

def main():
    """主测试流程"""
    print("RKLLM 模型综合基准测试")
    print("=" * 50)

    benchmark = ComprehensiveBenchmark()

    # 测试连接
    print("正在测试服务器连接...")
    success, _ = benchmark._send_request("hello", timeout=10)
    if not success:
        print("❌ 无法连接到服务器，请确保 flask_server.py 已启动")
        return
    print("✅ 服务器连接成功")

    try:
        print("\n请选择测试模式:")
        print("1. 快速测试 (基础功能)")
        print("2. 标准测试 (性能 + 准确性 + 一致性)")
        print("3. 全面测试 (所有测试项目)")
        print("4. 自定义测试")

        choice = input("请输入选择 (1-4): ").strip()

        if choice == "1":
            print("\n🚀 开始快速测试...")
            benchmark.test_qa_accuracy()

        elif choice == "2":
            print("\n🚀 开始标准测试...")
            benchmark.test_performance_metrics()
            benchmark.test_qa_accuracy()
            benchmark.test_consistency()

        elif choice == "3":
            print("\n🚀 开始全面测试...")
            benchmark.test_performance_metrics()
            benchmark.test_qa_accuracy()
            benchmark.test_consistency()
            benchmark.test_robustness()
            benchmark.test_reasoning_ability()
            benchmark.test_creativity()

        elif choice == "4":
            print("\n自定义测试选项:")
            print("a. 性能指标测试")
            print("b. 问答准确性测试")
            print("c. 一致性测试")
            print("d. 鲁棒性测试")
            print("e. 推理能力测试")
            print("f. 创造性测试")

            custom_choice = input("请输入测试项目 (如: abc): ").strip().lower()

            if 'a' in custom_choice:
                benchmark.test_performance_metrics()
            if 'b' in custom_choice:
                benchmark.test_qa_accuracy()
            if 'c' in custom_choice:
                benchmark.test_consistency()
            if 'd' in custom_choice:
                benchmark.test_robustness()
            if 'e' in custom_choice:
                benchmark.test_reasoning_ability()
            if 'f' in custom_choice:
                benchmark.test_creativity()
        else:
            print("无效选择")
            return

        # 生成综合报告
        if benchmark.results:
            benchmark.generate_comprehensive_report()
        else:
            print("没有测试结果可生成报告")

    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
