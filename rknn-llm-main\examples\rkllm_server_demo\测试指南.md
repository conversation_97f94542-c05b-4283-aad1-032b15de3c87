# RKLLM 模型全面测试指南

## 📋 测试标准体系

### 1. 性能指标 (Performance Metrics)

#### 延迟指标
- **首Token时间 (TTFT)**: 从请求到第一个token输出的时间
  - 🟢 优秀: < 1秒
  - 🟡 良好: 1-2秒  
  - 🟠 可接受: 2-3秒
  - 🔴 需优化: > 3秒

- **Token间延迟 (ITL)**: 相邻token之间的时间间隔
  - 🟢 优秀: < 50ms
  - 🟡 良好: 50-100ms
  - 🟠 可接受: 100-200ms
  - 🔴 需优化: > 200ms

#### 吞吐量指标
- **Token生成速率**: tokens/秒
  - 🟢 优秀: > 20 tokens/s
  - 🟡 良好: 15-20 tokens/s
  - 🟠 可接受: 10-15 tokens/s
  - 🔴 需优化: < 10 tokens/s

- **并发处理能力**: 同时处理的请求数
  - 🟢 优秀: > 5个并发
  - 🟡 良好: 3-5个并发
  - 🟠 可接受: 1-3个并发

### 2. 功能完整性 (Functional Completeness)

#### 基础对话能力
- **问答准确性**: 对事实性问题的正确回答率 (目标: >80%)
- **上下文理解**: 多轮对话中的上下文保持能力
- **指令遵循**: 按照用户指令执行任务的能力

#### 高级功能
- **Function Calling**: 工具调用的准确性和成功率
- **代码生成**: 编程相关任务的完成质量
- **数学计算**: 数学问题的求解准确性
- **逻辑推理**: 复杂推理任务的处理能力

### 3. 输出质量 (Output Quality)

#### 内容质量
- **相关性**: 回答与问题的相关程度
- **完整性**: 回答的全面性和详细程度
- **逻辑性**: 回答的逻辑结构和条理性
- **创造性**: 创意性任务的表现

#### 语言质量
- **流畅性**: 语言表达的自然流畅程度
- **语法正确性**: 语法错误率
- **词汇丰富性**: 用词的多样性和准确性
- **风格一致性**: 语言风格的统一性

### 4. 鲁棒性 (Robustness)

#### 输入处理能力
- **长文本处理**: 处理不同长度输入的能力
- **特殊字符处理**: 对特殊符号、表情等的处理
- **多语言支持**: 对不同语言的理解和生成能力
- **格式兼容性**: 对不同输入格式的适应性

#### 错误处理
- **异常输入处理**: 对无意义或恶意输入的处理
- **边界情况**: 极端情况下的表现
- **错误恢复**: 出错后的恢复能力

### 5. 一致性 (Consistency)

#### 输出一致性
- **重复性测试**: 相同输入多次测试的结果一致性 (目标: >70%相似度)
- **参数敏感性**: 不同参数设置下的表现稳定性
- **时间一致性**: 不同时间测试的结果稳定性

### 6. 压力承受能力 (Stress Tolerance)

#### 负载测试
- **并发处理**: 多用户同时访问的处理能力
- **持续负载**: 长时间运行的稳定性
- **突发流量**: 短时间大量请求的处理能力
- **资源使用**: 内存和CPU的使用效率

## 🚀 测试工具使用指南

### 1. 启动服务器

```bash
cd rknn-llm-main/examples/rkllm_server_demo/rkllm_server
python3 flask_server.py --rkllm_model_path ~/rknn/Qwen3-1.7B.rkllm --target_platform rk3588
```

### 2. 基础功能测试

#### 快速验证
```bash
cd rknn-llm-main/examples/rkllm_server_demo
python3 chat_api_flask.py
```

#### 全面基准测试
```bash
python3 comprehensive_benchmark.py
```

**测试选项:**
- 选项1: 快速测试 (仅基础功能)
- 选项2: 标准测试 (性能+准确性+一致性)
- 选项3: 全面测试 (所有测试项目)
- 选项4: 自定义测试 (选择特定测试项目)

### 3. 压力测试

```bash
python3 stress_test.py
```

**压力测试选项:**
- 轻度压力: 3线程, 5请求/线程
- 中度压力: 5线程, 10请求/线程  
- 重度压力: 10线程, 20请求/线程
- 持续负载: 5分钟持续测试
- 突发负载: 短时间大量请求
- 全面压力: 所有压力测试

### 4. 高级功能测试

```bash
python3 advanced_test.py
```

**高级功能包括:**
- Function Calling测试
- 流式输出测试
- 并发请求测试
- 质量评估测试

## 📊 测试报告解读

### 综合评分标准

#### 总体评级
- **A级 (90-100%)**: 优秀 - 模型表现卓越，可用于生产环境
- **B级 (80-89%)**: 良好 - 模型表现良好，适合大多数应用场景
- **C级 (70-79%)**: 中等 - 模型基本可用，但需要优化
- **D级 (60-69%)**: 及格 - 模型勉强可用，存在明显问题
- **F级 (<60%)**: 需改进 - 模型不适合实际使用

#### 各项指标权重
- **性能指标**: 30% (响应速度、吞吐量)
- **准确性**: 25% (问答正确率、功能完整性)
- **一致性**: 20% (输出稳定性、行为一致性)
- **鲁棒性**: 15% (异常处理、边界情况)
- **创造性**: 10% (创新能力、语言质量)

### 关键性能指标 (KPI)

#### 必达指标 (生产环境最低要求)
- ✅ 首Token时间 < 3秒
- ✅ Token生成速率 > 10 tokens/s
- ✅ 问答准确率 > 70%
- ✅ 系统稳定性 > 95%
- ✅ 并发处理 ≥ 3用户

#### 优秀指标 (高质量服务标准)
- 🎯 首Token时间 < 1秒
- 🎯 Token生成速率 > 20 tokens/s
- 🎯 问答准确率 > 85%
- 🎯 系统稳定性 > 99%
- 🎯 并发处理 ≥ 5用户

## 🔧 优化建议

### 性能优化
1. **模型量化**: 使用更激进的量化策略
2. **批处理**: 实现请求批处理机制
3. **缓存策略**: 添加常见问题缓存
4. **硬件优化**: 调整NPU频率和内存配置

### 准确性提升
1. **模型微调**: 针对特定领域进行微调
2. **提示工程**: 优化系统提示词
3. **后处理**: 添加输出质量检查
4. **知识库**: 集成外部知识源

### 稳定性增强
1. **错误处理**: 完善异常处理机制
2. **资源监控**: 添加系统资源监控
3. **负载均衡**: 实现多实例负载均衡
4. **降级策略**: 设计服务降级方案

## 📝 测试日志分析

### 日志级别说明
- **INFO**: 正常测试进度信息
- **WARNING**: 测试中的警告信息
- **ERROR**: 测试失败或错误信息

### 常见问题排查

#### 连接问题
```
❌ 无法连接到服务器
```
**解决方案**: 检查flask_server.py是否正常启动，端口是否被占用

#### 超时问题
```
请求超时 (timeout)
```
**解决方案**: 增加timeout参数，检查模型加载是否完成

#### 内存不足
```
内存使用率过高
```
**解决方案**: 减少并发数，优化模型配置，增加系统内存

#### 响应质量差
```
准确率低于预期
```
**解决方案**: 检查模型版本，调整生成参数，考虑模型微调

## 🎯 测试最佳实践

### 测试前准备
1. 确保系统资源充足 (内存 > 8GB)
2. 关闭不必要的后台程序
3. 检查网络连接稳定性
4. 备份重要数据

### 测试执行
1. 从轻度测试开始，逐步增加强度
2. 记录每次测试的环境参数
3. 多次重复关键测试确保结果可靠
4. 及时保存测试报告和日志

### 结果分析
1. 对比不同配置下的测试结果
2. 关注性能瓶颈和异常情况
3. 分析失败案例的根本原因
4. 制定针对性的优化方案

### 持续改进
1. 定期执行回归测试
2. 跟踪性能指标变化趋势
3. 收集用户反馈进行测试优化
4. 更新测试用例和评估标准

---

**注意**: 测试过程中请确保模型服务正常运行，避免在生产环境中执行高强度压力测试。
