#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKLLM 压力测试脚本
测试模型在高负载情况下的性能和稳定性
"""

import requests
import json
import time
import threading
import statistics
import psutil
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass
from typing import List, Dict, Any
import sys

@dataclass
class StressTestResult:
    """压力测试结果"""
    test_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    requests_per_second: float
    error_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float

class StressTester:
    def __init__(self, server_url='http://127.0.0.1:8080/rkllm_chat'):
        self.server_url = server_url
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': 'not_required'
        }
        
        # 测试问题集
        self.test_questions = [
            "你好，请介绍一下你自己",
            "请解释一下人工智能的基本概念",
            "1+1等于几？请详细解释",
            "请写一首关于春天的诗",
            "什么是机器学习？",
            "请列举三种编程语言",
            "解释一下深度学习的原理",
            "中国的首都是哪里？",
            "请计算 15 * 8 的结果",
            "描述一下你对未来科技的看法"
        ]
        
        self.results: List[StressTestResult] = []
    
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def send_single_request(self, question, timeout=30):
        """发送单个请求"""
        start_time = time.time()
        try:
            data = {
                "model": "stress_test",
                "messages": [{"role": "user", "content": question}],
                "stream": False
            }
            
            response = requests.post(
                self.server_url,
                json=data,
                headers=self.headers,
                timeout=timeout
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                return True, response_time, None
            else:
                return False, response_time, f"HTTP {response.status_code}"
                
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            return False, response_time, str(e)
    
    def concurrent_load_test(self, num_threads=5, requests_per_thread=10):
        """并发负载测试"""
        self.log(f"=== 开始并发负载测试 ({num_threads} 线程, 每线程 {requests_per_thread} 请求) ===")
        
        total_requests = num_threads * requests_per_thread
        successful_requests = 0
        failed_requests = 0
        response_times = []
        errors = []
        
        # 监控系统资源
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        def worker_thread(thread_id):
            """工作线程函数"""
            thread_results = []
            for i in range(requests_per_thread):
                question = self.test_questions[i % len(self.test_questions)]
                success, response_time, error = self.send_single_request(question)
                
                thread_results.append({
                    'success': success,
                    'response_time': response_time,
                    'error': error,
                    'thread_id': thread_id,
                    'request_id': i
                })
                
                # 短暂延迟避免过于密集的请求
                time.sleep(0.1)
            
            return thread_results
        
        # 开始测试
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = []
            for thread_id in range(num_threads):
                future = executor.submit(worker_thread, thread_id)
                futures.append(future)
            
            # 收集结果
            for future in as_completed(futures):
                thread_results = future.result()
                for result in thread_results:
                    if result['success']:
                        successful_requests += 1
                        response_times.append(result['response_time'])
                    else:
                        failed_requests += 1
                        errors.append(result['error'])
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 计算最终内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_usage = final_memory - initial_memory
        
        # 计算CPU使用率 (简化版本)
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 计算统计数据
        avg_response_time = statistics.mean(response_times) if response_times else 0
        min_response_time = min(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        requests_per_second = total_requests / total_time if total_time > 0 else 0
        error_rate = (failed_requests / total_requests) * 100 if total_requests > 0 else 0
        
        # 保存结果
        result = StressTestResult(
            test_name=f"并发负载测试 ({num_threads}线程)",
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=requests_per_second,
            error_rate=error_rate,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage
        )
        
        self.results.append(result)
        
        # 输出结果
        self.log(f"✓ 并发测试完成")
        self.log(f"  总请求数: {total_requests}")
        self.log(f"  成功请求: {successful_requests}")
        self.log(f"  失败请求: {failed_requests}")
        self.log(f"  成功率: {((successful_requests/total_requests)*100):.1f}%")
        self.log(f"  平均响应时间: {avg_response_time:.2f}s")
        self.log(f"  请求速率: {requests_per_second:.2f} req/s")
        self.log(f"  内存增长: {memory_usage:.1f} MB")
        self.log(f"  CPU使用率: {cpu_usage:.1f}%")
        
        if errors:
            self.log(f"  错误类型: {set(errors)}")
    
    def sustained_load_test(self, duration_minutes=5, requests_per_minute=12):
        """持续负载测试"""
        self.log(f"=== 开始持续负载测试 ({duration_minutes} 分钟, {requests_per_minute} req/min) ===")
        
        duration_seconds = duration_minutes * 60
        interval = 60 / requests_per_minute  # 请求间隔
        
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        response_times = []
        
        start_time = time.time()
        next_request_time = start_time
        
        while time.time() - start_time < duration_seconds:
            current_time = time.time()
            
            if current_time >= next_request_time:
                question = self.test_questions[total_requests % len(self.test_questions)]
                success, response_time, error = self.send_single_request(question, timeout=15)
                
                total_requests += 1
                if success:
                    successful_requests += 1
                    response_times.append(response_time)
                else:
                    failed_requests += 1
                    self.log(f"请求失败: {error}", "WARNING")
                
                next_request_time += interval
                
                # 每分钟输出一次进度
                if total_requests % requests_per_minute == 0:
                    elapsed_minutes = (current_time - start_time) / 60
                    success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
                    avg_time = statistics.mean(response_times) if response_times else 0
                    self.log(f"进度: {elapsed_minutes:.1f}分钟, 成功率: {success_rate:.1f}%, 平均响应: {avg_time:.2f}s")
            
            time.sleep(0.1)  # 短暂休眠
        
        total_time = time.time() - start_time
        
        # 计算统计数据
        avg_response_time = statistics.mean(response_times) if response_times else 0
        min_response_time = min(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        requests_per_second = total_requests / total_time if total_time > 0 else 0
        error_rate = (failed_requests / total_requests) * 100 if total_requests > 0 else 0
        
        # 保存结果
        result = StressTestResult(
            test_name=f"持续负载测试 ({duration_minutes}分钟)",
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=requests_per_second,
            error_rate=error_rate,
            memory_usage_mb=0,  # 持续测试中不易准确测量
            cpu_usage_percent=0
        )
        
        self.results.append(result)
        
        self.log(f"✓ 持续测试完成")
        self.log(f"  实际运行时间: {total_time/60:.1f} 分钟")
        self.log(f"  总请求数: {total_requests}")
        self.log(f"  成功率: {((successful_requests/total_requests)*100):.1f}%")
        self.log(f"  平均响应时间: {avg_response_time:.2f}s")
    
    def burst_test(self, burst_size=20, num_bursts=3, burst_interval=30):
        """突发负载测试"""
        self.log(f"=== 开始突发负载测试 ({num_bursts} 次突发, 每次 {burst_size} 请求) ===")
        
        all_results = []
        
        for burst_num in range(num_bursts):
            self.log(f"执行第 {burst_num + 1} 次突发测试...")
            
            # 突发请求
            start_time = time.time()
            
            def send_burst_request(request_id):
                question = self.test_questions[request_id % len(self.test_questions)]
                return self.send_single_request(question, timeout=20)
            
            with ThreadPoolExecutor(max_workers=burst_size) as executor:
                futures = [executor.submit(send_burst_request, i) for i in range(burst_size)]
                burst_results = [future.result() for future in as_completed(futures)]
            
            end_time = time.time()
            burst_time = end_time - start_time
            
            # 统计突发结果
            successful = sum(1 for success, _, _ in burst_results if success)
            failed = burst_size - successful
            response_times = [rt for success, rt, _ in burst_results if success]
            
            avg_response_time = statistics.mean(response_times) if response_times else 0
            
            self.log(f"  突发 {burst_num + 1}: {successful}/{burst_size} 成功, 平均响应: {avg_response_time:.2f}s, 突发时间: {burst_time:.2f}s")
            
            all_results.extend(burst_results)
            
            # 等待下一次突发
            if burst_num < num_bursts - 1:
                self.log(f"  等待 {burst_interval} 秒后进行下一次突发...")
                time.sleep(burst_interval)
        
        # 汇总所有突发结果
        total_requests = len(all_results)
        successful_requests = sum(1 for success, _, _ in all_results if success)
        failed_requests = total_requests - successful_requests
        response_times = [rt for success, rt, _ in all_results if success]
        
        avg_response_time = statistics.mean(response_times) if response_times else 0
        min_response_time = min(response_times) if response_times else 0
        max_response_time = max(response_times) if response_times else 0
        error_rate = (failed_requests / total_requests) * 100 if total_requests > 0 else 0
        
        result = StressTestResult(
            test_name=f"突发负载测试 ({num_bursts}次突发)",
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            requests_per_second=0,  # 突发测试中不适用
            error_rate=error_rate,
            memory_usage_mb=0,
            cpu_usage_percent=0
        )
        
        self.results.append(result)
        
        self.log(f"✓ 突发测试完成")
        self.log(f"  总成功率: {((successful_requests/total_requests)*100):.1f}%")
        self.log(f"  平均响应时间: {avg_response_time:.2f}s")
    
    def generate_stress_report(self):
        """生成压力测试报告"""
        self.log("=== 生成压力测试报告 ===")
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("RKLLM 模型压力测试报告")
        report_lines.append("=" * 80)
        report_lines.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"服务器地址: {self.server_url}")
        report_lines.append("")
        
        for result in self.results:
            report_lines.append(f"【{result.test_name}】")
            report_lines.append("-" * 40)
            report_lines.append(f"总请求数: {result.total_requests}")
            report_lines.append(f"成功请求: {result.successful_requests}")
            report_lines.append(f"失败请求: {result.failed_requests}")
            report_lines.append(f"成功率: {((result.successful_requests/result.total_requests)*100):.1f}%")
            report_lines.append(f"平均响应时间: {result.avg_response_time:.2f}s")
            report_lines.append(f"最小响应时间: {result.min_response_time:.2f}s")
            report_lines.append(f"最大响应时间: {result.max_response_time:.2f}s")
            
            if result.requests_per_second > 0:
                report_lines.append(f"请求速率: {result.requests_per_second:.2f} req/s")
            
            if result.memory_usage_mb > 0:
                report_lines.append(f"内存增长: {result.memory_usage_mb:.1f} MB")
            
            if result.cpu_usage_percent > 0:
                report_lines.append(f"CPU使用率: {result.cpu_usage_percent:.1f}%")
            
            report_lines.append("")
        
        # 总体评估
        if self.results:
            avg_success_rate = statistics.mean([
                (r.successful_requests / r.total_requests) * 100 
                for r in self.results if r.total_requests > 0
            ])
            
            avg_response_time = statistics.mean([
                r.avg_response_time for r in self.results
            ])
            
            report_lines.append("【总体评估】")
            report_lines.append("-" * 40)
            report_lines.append(f"平均成功率: {avg_success_rate:.1f}%")
            report_lines.append(f"平均响应时间: {avg_response_time:.2f}s")
            
            # 压力测试评级
            if avg_success_rate >= 95 and avg_response_time <= 3.0:
                grade = "优秀 - 系统在高负载下表现稳定"
            elif avg_success_rate >= 90 and avg_response_time <= 5.0:
                grade = "良好 - 系统基本能承受压力"
            elif avg_success_rate >= 80:
                grade = "中等 - 系统在压力下有一定问题"
            else:
                grade = "需改进 - 系统压力承受能力不足"
            
            report_lines.append(f"压力测试评级: {grade}")
        
        report_lines.append("=" * 80)
        
        # 输出报告
        for line in report_lines:
            print(line)
        
        # 保存报告
        with open('stress_test_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        self.log("压力测试报告已保存到 stress_test_report.txt")

def main():
    """主测试流程"""
    print("RKLLM 模型压力测试工具")
    print("=" * 50)
    
    tester = StressTester()
    
    # 测试连接
    print("正在测试服务器连接...")
    success, _, _ = tester.send_single_request("hello", timeout=10)
    if not success:
        print("❌ 无法连接到服务器，请确保 flask_server.py 已启动")
        return
    print("✅ 服务器连接成功")
    
    try:
        print("\n请选择压力测试类型:")
        print("1. 轻度压力测试 (3线程, 5请求/线程)")
        print("2. 中度压力测试 (5线程, 10请求/线程)")
        print("3. 重度压力测试 (10线程, 20请求/线程)")
        print("4. 持续负载测试 (5分钟, 12请求/分钟)")
        print("5. 突发负载测试 (3次突发, 每次20请求)")
        print("6. 全面压力测试 (所有测试)")
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == "1":
            tester.concurrent_load_test(num_threads=3, requests_per_thread=5)
        elif choice == "2":
            tester.concurrent_load_test(num_threads=5, requests_per_thread=10)
        elif choice == "3":
            tester.concurrent_load_test(num_threads=10, requests_per_thread=20)
        elif choice == "4":
            tester.sustained_load_test(duration_minutes=5, requests_per_minute=12)
        elif choice == "5":
            tester.burst_test(burst_size=20, num_bursts=3, burst_interval=30)
        elif choice == "6":
            tester.concurrent_load_test(num_threads=5, requests_per_thread=10)
            tester.sustained_load_test(duration_minutes=3, requests_per_minute=10)
            tester.burst_test(burst_size=15, num_bursts=2, burst_interval=20)
        else:
            print("无效选择")
            return
        
        # 生成报告
        if tester.results:
            tester.generate_stress_report()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
